<script setup lang="ts">
import { ref } from 'vue'
import SingleChoice from './components/q-single-choice/index'

defineOptions({
  name: 'RightView',
})

// 是否显示题目结果
const showQuestionResult = ref(true)

// 题目数据
const questionData = ref({
  title: 'AI一共为您生成了 1 道 单选题 1 道',
  type: '单选题',
  content: '小巧看完一本352页的童话书，第一周看了135页，第二周看了165页，还剩下多少页没有看完？关于如何解决问题，下列说法正确的是（ ）。',
  options: [
    { label: '只能列算式352－135－165解答。', value: 'A' },
    { label: '只能列算式352－（135+165）解答。', value: 'B' },
    { label: '两个算式352－135－165或352－（135+165）都可以解答。', value: 'C' },
    { label: '两个算式352－135－165或352－（135+165）都不可以解答。', value: 'D' },
  ],
  correctAnswer: 'C',
  analysis: {
    title: '答案解析：',
    content: [
      '（1）用总页数依次减去第一周看的页数和第二周看的页数，得到剩下没看的页数。',
      '（2）算式为352-135-165。',
      '（3）按照这个算式的计算顺序，先算352-135=217（页），再算217-165=52（页）。',
    ],
  },
  knowledgePoints: ['减法的意义', '运算顺序', '加法和减法的混合运算'],
})

// 选中的答案
const selectedAnswer = ref('')

// 操作方法
function clearAll() {
  console.log('一键清空')
}

function addToBank() {
  console.log('加入题库')
}

function replaceQuestion() {
  console.log('换一题')
}

function addToTest() {
  console.log('加入题库')
}

function editQuestion() {
  console.log('编辑')
}

function deleteQuestion() {
  console.log('删除')
}
</script>

<template>
  <div class="h-full w-full bg-white p-12px">
    <div v-if="showQuestionResult" class="h-full">
      <NScrollbar class="h-full">
        <!-- 顶部标题栏 -->
        <div class="mb-6 flex items-center justify-between border-b border-gray-100 pb-4">
          <div class="flex items-center gap-2">
            <div class="h-4 w-4 rounded-sm bg-blue-500" />
            <span class="text-lg text-gray-800 font-medium">本次出题结果</span>
          </div>
          <div class="flex gap-3">
            <NButton
              quaternary
              type="primary"
              class="border border-blue-400 rounded-full text-blue-500 hover:bg-blue-50"
              @click="clearAll"
            >
              一键清空
            </NButton>
            <NButton
              type="primary"
              class="rounded-full"
              @click="addToBank"
            >
              加入题库
            </NButton>
          </div>
        </div>

        <!-- AI生成信息 -->
        <div class="mb-6 rounded-lg bg-gray-50 px-4 py-3 text-gray-600">
          {{ questionData.title }}
        </div>

        <!-- 题目卡片 -->
        <div class="border border-gray-200 rounded-lg bg-white p-6 shadow-sm">
          <!-- 题目类型和操作按钮 -->
          <div class="mb-4 flex items-center justify-between">
            <div class="flex items-center gap-2">
              <NCheckbox />
              <span class="text-gray-600">{{ questionData.type }}</span>
            </div>
            <div class="flex items-center gap-4 text-blue-500">
              <NButton text class="flex items-center gap-1" @click="replaceQuestion">
                <SvgIcon icon="mdi:refresh" class="h-4 w-4" />
                换一题
              </NButton>
              <NButton text class="flex items-center gap-1" @click="addToTest">
                <SvgIcon icon="mdi:plus" class="h-4 w-4" />
                加入题库
              </NButton>
              <NButton text class="flex items-center gap-1" @click="editQuestion">
                <SvgIcon icon="mdi:pencil" class="h-4 w-4" />
                编辑
              </NButton>
              <NButton text class="flex items-center gap-1" @click="deleteQuestion">
                <SvgIcon icon="mdi:delete" class="h-4 w-4" />
                删除
              </NButton>
            </div>
          </div>

          <!-- 题目内容 -->
          <div class="mb-6 text-gray-800 leading-relaxed">
            {{ questionData.content }}
          </div>

          <!-- 选项 - 使用q-single-choice组件 -->
          <div class="mb-6">
            <SingleChoice
              v-model="selectedAnswer"
              :options="questionData.options"
              :disabled="true"
            />
          </div>

          <!-- 正确答案 -->
          <div class="mb-6">
            <span class="text-green-600 font-medium">正确答案：{{ questionData.correctAnswer }}</span>
          </div>

          <!-- 答案解析 -->
          <div class="mb-6">
            <div class="mb-2 text-gray-800 font-medium">
              {{ questionData.analysis.title }}
            </div>
            <div class="space-y-1">
              <div
                v-for="(item, index) in questionData.analysis.content"
                :key="index"
                class="text-gray-700 leading-relaxed"
              >
                {{ item }}
              </div>
            </div>
          </div>

          <!-- 知识点 -->
          <div class="flex flex-wrap items-center gap-2">
            <span class="text-gray-600">知识点：</span>
            <div class="flex flex-wrap gap-2">
              <NTag
                v-for="point in questionData.knowledgePoints"
                :key="point"
                type="info"
                size="small"
                class="border border-blue-200 rounded-full bg-blue-50 px-3 py-1 text-blue-600"
              >
                {{ point }}
              </NTag>
            </div>
          </div>
        </div>
      </NScrollbar>
    </div>

    <!-- 内容为空状态 -->
    <div v-else class="text-group_3 empty h-full flex flex-col items-center justify-center">
      <img src="@/assets/imgs/empty.png" alt="" class="h-272px w-272px">
      <span class="text-center text-20px">出题指南</span>
      <span class="mt-14px text-center text-18px text-[rgba(172,172,172,1)] font-normal">
        1.选择出题方式，境好必要的信息<br>
        2.系统将自动保存您的出题设置<br>
        3.&nbsp;3. 点击”立即出题”，题目结果将显示在右侧
      </span>
    </div>
  </div>
</template>

<style scoped>
/* 自定义样式 */
</style>
